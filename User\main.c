#include "Hardware.h"

uint8_t read_data_command[8] = {0xff, 0x03, 0x60, 0x01, 0x00, 0x01, 0xde, 0x14};

//uint16_t temp;

int main(void)
{	
	hardware_initializes();
	software_initializes();
	
	while(1)
	{
		if(timer2_management.flag_100ms)
		{
			timer2_management.flag_100ms = 0;
			
		}
		/*每个一秒通过CAN发送数据*/
		if(timer2_management.flag_1s)
		{
			timer2_management.flag_1s = 0;
			
			CAN_SendData(0x20A, data_management.concentration, 8);
			
		}
	}
}



